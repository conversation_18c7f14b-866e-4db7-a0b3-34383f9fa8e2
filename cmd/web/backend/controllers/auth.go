package controllers

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

type LoginInput struct {
	Email    string `form:"email" json:"email" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}

func Home(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "You have successfully connected",
	})
}
func Login(c *gin.Context) {
	var login LoginInput
	err := c.ShouldBindJSON(&login)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}
	var result models.User
	userType := "backend"
	err = config.DB.Model(models.User{Email: login.Email, UserType: &userType}).First(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.<PERSON>(http.StatusNotFound, gin.H{
				"message": "User not found",
			})
		} else {
			fmt.Println("This is where the error occurred")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": err.Error(),
			})
		}
		return
	}
	if !service.CheckPassword(result.Password, login.Password) {
		c.JSON(422, gin.H{
			"message": "Invalid login details",
		})
		fmt.Println("returned on invalid")
		return
	}
	// SELECT * FROM users WHERE id = 10;
	fmt.Println("Generating token")
	token, er := service.GenerateToken(&result, "access")
	if er != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": er.Error(),
		})
		return
	}
	//set last login date
	config.DB.Model(&result).Update("last_login_date", time.Now())
	fmt.Println("token generated")
	c.JSON(200, gin.H{
		"token": token,
	})
}
func Register(c *gin.Context) {

}
