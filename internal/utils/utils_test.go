package utils

import (
	"testing"
	"yotracker/config"
	"yotracker/internal/seed"
)

func SetupTestDBWithSeed(t *testing.T) {
	ForceProjectRoot()
	t.Helper()
	config.InitTestDB()
	seed.Seed()
}

func TestGenerateReference(t *testing.T) {
	// Initialize test database
	SetupTestDBWithSeed(t)

	//test for INV-2025/12/001
	result := GenerateReference("1")

	// Should contain current year
	if len(result) < 10 { // INV-2024/001 = 11 chars minimum
		t.Errorf("GenerateReference() with year format too short: %v", result)
	}

	// Should start with prefix
	if result[:4] != "INV-" {
		t.Errorf("GenerateReference() should start with INV-, got: %v", result)
	}

	// Should contain a slash
	found := false
	for _, char := range result {
		if char == '/' {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("GenerateReference() with year format should contain '/', got: %v", result)
	}
}
func TestGenerateUniqueID(t *testing.T) {
	id1 := generateUniqueID()
	id2 := generateUniqueID()

	if id1 == id2 {
		t.Errorf("generateUniqueID() should generate unique IDs, got same: %v", id1)
	}

	if len(id1) != 16 { // 8 bytes = 16 hex characters
		t.Errorf("generateUniqueID() should return 16 characters, got %v: %v", len(id1), id1)
	}
}
