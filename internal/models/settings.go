package models

import "time"

type Setting struct {
	Id           uint      `json:"id" gorm:"primaryKey"`
	Name         string    `json:"name"`
	SettingKey   string    `json:"setting_key" gorm:"unique"`
	SettingValue string    `json:"setting_value"`
	Category     *string   `json:"category" gorm:"type:varchar(255);default:general"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
